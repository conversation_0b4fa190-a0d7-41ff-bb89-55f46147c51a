import { EventManager } from "../systems/EventManager";
import { CharacterAttributeName } from "../types/CharacterTypes";
import FightEvent from "../types/FightEvent";
import { IBuff, BuffType, IBuffPeriodicEffect } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { IAttributeModifier, AttributeModifierType } from "../types/ICharacterAttributes";
import { IDamageInfo, DamageType } from "../types/IDamage";
import { AttributeModifier } from "../characters/AttributeModifier";
import { EBuffEffectType } from "../types/Buff";


/*** 受伤反击Buff实现*/
export class BuffModelBeHurtFight implements IBuff {
    private _id: string = "buff_be_hurt_fight";
    private _name: string = "受伤反击";
    private _description: string = "受到伤害时有概率反击攻击者";
    private _type: BuffType = BuffType.BUFF;
    /**持续时间，以s计算 */
    private _duration: number = 30.0;
    private _remainingTime: number;
    private _stackCount: number = 1;
    private _maxStack: number = 3;
    private _caster: ICharacter;
    private _target: ICharacter;
    private _isExpired: boolean = false;
    private _attributeModifiers: IAttributeModifier[] = [];
    private _periodicEffect?: IBuffPeriodicEffect;
    private _iconPath: string = "textures/buffs/be_hurt_fight";
    private _effectPrefabPath: string = "prefabs/effects/CounterAttackAura";
    private _eventManager: EventManager;
    /**Buff特有属性  - 默认 30%反击概率 */
    private _counterAttackChance: number = 0.3; //
    /**反击伤害倍率， 默认 1.5*/
    private _counterAttackDamageMultiplier: number = 1.5;

    constructor(caster: ICharacter, target: ICharacter) {
        this._caster = caster;
        this._target = target;
        this._remainingTime = this._duration;
        this._eventManager = new EventManager();
        this.initializeAttributeModifiers();
    }

    // 实现IBuff接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get type(): BuffType { return this._type; }
    get duration(): number { return this._duration; }
    get remainingTime(): number { return this._remainingTime; }
    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }
    get stackCount(): number { return this._stackCount; }
    set stackCount(value: number) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); }
    get maxStack(): number { return this._maxStack; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter { return this._target; }
    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }
    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }
    get periodicEffect(): IBuffPeriodicEffect | undefined { return this._periodicEffect; }
    get iconPath(): string | undefined { return this._iconPath; }
    get effectPrefabPath(): string | undefined { return this._effectPrefabPath; }

    /*** 初始化属性修改器*/
    protected initializeAttributeModifiers(): void {
        // 增加攻击力（每层增加10%）
        const attackModifier = new AttributeModifier(
            `${this._id}_attack_modifier`,
            "反击攻击力加成",
            "attack",
            AttributeModifierType.PERCENTAGE,
            0.1 * this._stackCount, // 每层10%
            this._duration
        );

        // 增加暴击率（每层增加5%）
        const criticalRateModifier = new AttributeModifier(
            `${this._id}_critical_rate_modifier`,
            "反击暴击率加成",
            CharacterAttributeName.criticalRate,
            AttributeModifierType.ADD,
            0.05 * this._stackCount, // 每层5%
            this._duration
        );

        this._attributeModifiers = [attackModifier, criticalRateModifier];
    }

    /*** Buff被添加时触发*/
    onApply(): void {
        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);
        // 应用属性修改器到目标
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.addModifier(modifier);
        }
        // 播放特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });
    }

    /*** Buff每帧更新时触发*/
    onTick(deltaTime: number): void {
        // 更新属性修改器
        for (const modifier of this._attributeModifiers) {
            modifier.update(deltaTime);
        }
        // 这里可以添加其他每帧更新的逻辑
        // 比如粒子特效的更新等
    }

    /** * Buff被移除时触发 */
    onRemove(): void {
        console.log(`${this._name} removed from ${this._target.characterName}`);
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.removeModifier(modifier.id);
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });
    }

    /** * 受到伤害时触发（核心功能） */
    onTakeDamage(damageInfo: IDamageInfo, attacker: ICharacter): IDamageInfo {
        // 检查是否触发反击
        if (this.shouldCounterAttack(damageInfo, attacker)) {
            this.performCounterAttack(attacker);
        }
        // 返回原始伤害信息（不修改伤害）
        return damageInfo;
    }

    /** * 更新Buff状态 */
    update(deltaTime: number): boolean {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        // 检查是否过期
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    }

    /** * 刷新Buff持续时间 */
    refresh(): void {
        this._remainingTime = this._duration;
        this._isExpired = false;
        console.log(`${this._name} refreshed on ${this._target.characterName}`);
        this._eventManager.emit(FightEvent.buffRefreshed, { buff: this, target: this._target });
    }

    /** * 增加叠加层数 */
    addStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.min(this._maxStack, this._stackCount + count);
        if (this._stackCount !== oldStack) {
            // 重新初始化属性修改器以反映新的层数
            this.updateAttributeModifiersForStack();
            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target, oldStack, newStack: this._stackCount });
        }
    }
    /** * 减少叠加层数 */
    removeStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.max(0, this._stackCount - count);
        if (this._stackCount !== oldStack) {
            if (this._stackCount === 0) {
                this._isExpired = true;
            } else {
                // 重新初始化属性修改器以反映新的层数
                this.updateAttributeModifiersForStack();
            }
            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target, oldStack, newStack: this._stackCount });
        }
    }
    /*** 获取Buff的当前效果值*/
    getEffectValue(effectType: string): number {
        switch (effectType) {
            case EBuffEffectType.counterAttackChance: return this._counterAttackChance;
            case EBuffEffectType.counterAttackDamageMultiplier: return this._counterAttackDamageMultiplier;
            case EBuffEffectType.attackBonus: return 0.1 * this._stackCount;
            case EBuffEffectType.criticalRateBonus: return 0.05 * this._stackCount;
            default: return 0;
        }
    }
    /*** 检查Buff是否与另一个Buff冲突*/
    conflictsWith(otherBuff: IBuff): boolean {
        // 同类型的反击Buff冲突
        return otherBuff.id === this._id || otherBuff.id.includes("counter_attack");
    }

    /** * 检查是否应该反击 */
    private shouldCounterAttack(damageInfo: IDamageInfo, attacker: ICharacter): boolean {
        // 检查攻击者是否有效
        if (!attacker || attacker.isDead || attacker === this._target) {
            return false;
        }
        // 检查伤害类型（只对直接伤害反击）
        if (damageInfo.damageType === DamageType.TRUE) {
            return false; // 真实伤害不触发反击
        }
        // 概率检查
        const random = Math.random();
        const chance = this._counterAttackChance * this._stackCount; // 每层增加反击概率
        return random < chance;
    }

    /** * 执行反击 */
    private performCounterAttack(attacker: ICharacter): void {
        console.log(`${this._target.characterName} counter attacks ${attacker.characterName}!`);
        // 计算反击伤害
        const baseDamage = this._target.attributes.attack;
        const counterDamage = Math.floor(baseDamage * this._counterAttackDamageMultiplier);
        // 造成反击伤害
        attacker.takeDamage(counterDamage, this._target);
        // 播放反击特效
        this.playCounterAttackEffect();
        // 触发反击事件
        this._eventManager.emit(FightEvent.counterAttack, { buff: this, attacker: this._target, victim: attacker, damage: counterDamage });
    }

    /** * 更新属性修改器以反映新的层数 */
    private updateAttributeModifiersForStack(): void {
        // 移除旧的修改器
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.removeModifier(modifier.id);
        }

        // 重新初始化修改器
        this._attributeModifiers.length = 0;
        this.initializeAttributeModifiers();

        // 应用新的修改器
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.addModifier(modifier);
        }
    }

    /** * 播放应用特效 */
    protected playApplyEffect(): void {
        // 这里应该调用特效管理器
        console.log(`Playing apply effect for ${this._name}`);
    }
    /** * 停止特效 */
    protected stopEffect(): void {
        // 这里应该停止特效
        console.log(`Stopping effect for ${this._name}`);
    }
    /** * 播放反击特效 */
    protected playCounterAttackEffect(): void {
        // 这里应该播放反击特效
        console.log(`Playing counter attack effect for ${this._name}`);
    }
    /**  * 清理资源  */
    cleanup(): void {
        this._eventManager.cleanup();
        this._attributeModifiers.length = 0;
    }
}
