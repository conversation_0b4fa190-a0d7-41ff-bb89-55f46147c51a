import { EventManager } from "../systems/EventManager";
import FightEvent from "../types/FightEvent";
import { IBuff, BuffType, IBuffPeriodicEffect, BuffPeriodicEffectType } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { IAttributeModifier } from "../types/ICharacterAttributes";

/**
 * 持续治疗Buff
 * 每秒恢复一定生命值的增益效果
 */
export class HealOverTimeBuff implements IBuff {
    private _id: string;
    private _name: string = "持续治疗";
    private _description: string = "每秒恢复生命值";
    private _type: BuffType = BuffType.BUFF;
    private _duration: number;
    private _remainingTime: number;
    private _stackCount: number = 1;
    private _maxStack: number = 3;
    private _caster: ICharacter;
    private _target: ICharacter;
    private _isExpired: boolean = false;
    private _attributeModifiers: IAttributeModifier[] = [];
    private _eventManager: EventManager;

    // 持续治疗配置
    private _healPerSecond: number;
    private _healInterval: number = 1.0; // 每秒触发一次
    private _lastHealTime: number = 0;

    // 视觉效果
    private _iconPath: string = "icons/buffs/heal_over_time";
    private _effectPrefabPath: string = "prefabs/effects/HealAura";

    constructor(caster: ICharacter, target: ICharacter, duration: number, healPerSecond: number) {
        this._id = `heal_over_time_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._healPerSecond = healPerSecond;
        this._eventManager = EventManager.createLocal(`buff_${this._id}`);

        this._description = `每秒恢复${healPerSecond}点生命值，持续${duration}秒`;
    }

    // 实现IBuff接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get type(): BuffType { return this._type; }
    get duration(): number { return this._duration; }
    get remainingTime(): number { return this._remainingTime; }
    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }
    get stackCount(): number { return this._stackCount; }
    set stackCount(value: number) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); }
    get maxStack(): number { return this._maxStack; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter { return this._target; }
    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }
    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }
    get iconPath(): string { return this._iconPath; }
    get effectPrefabPath(): string { return this._effectPrefabPath; }

    get periodicEffect(): IBuffPeriodicEffect {
        return {
            interval: this._healInterval,
            effectType: BuffPeriodicEffectType.HEAL_OVER_TIME,
            value: this._healPerSecond,
            stackable: true
        };
    }

    /** Buff被添加时触发 */
    onApply(): void {
        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);

        // 播放应用特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }

        // 触发事件
        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });
    }

    /** Buff每帧更新时触发 */
    onTick(deltaTime: number): void {
        this._lastHealTime += deltaTime;

        // 检查是否到了治疗时间
        if (this._lastHealTime >= this._healInterval) {
            this.performHeal();
            this._lastHealTime = 0;
        }
    }

    /** Buff被移除时触发 */
    onRemove(): void {
        console.log(`${this._name} removed from ${this._target.characterName}`);
        this.stopEffect();
        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });
    }

    /** 执行治疗 */
    private performHeal(): void {
        if (this._target && !this._target.isDead) {
            const totalHeal = this._healPerSecond * this._stackCount;
            this._target.heal(totalHeal);
            console.log(`${this._target.characterName} healed for ${totalHeal} HP from ${this._name}`);

            // 播放治疗特效
            this.playHealEffect();

            // 触发治疗事件
            this._eventManager.emit(FightEvent.characterHealed, {
                target: this._target,
                healer: this._caster,
                amount: totalHeal,
                source: this
            });
        }
    }

    /** 更新Buff状态 */
    update(deltaTime: number): boolean {
        if (this._isExpired) {
            return true;
        }

        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);

        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }

        return false;
    }

    /** 刷新Buff持续时间 */
    refresh(): void {
        this._remainingTime = this._duration;
        this._lastHealTime = 0;
        console.log(`${this._name} refreshed on ${this._target.characterName}`);
    }

    /** 增加叠加层数 */
    addStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);

        if (this._stackCount > oldStack) {
            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });
        }
    }

    /** 减少叠加层数 */
    removeStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);

        if (this._stackCount < oldStack) {
            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });
        }
    }

    /** 获取Buff的当前效果值 */
    getEffectValue(effectType: string): number {
        switch (effectType) {
            case "heal_per_second":
                return this._healPerSecond * this._stackCount;
            case "total_heal":
                return this._healPerSecond * this._stackCount * this._remainingTime;
            default:
                return 0;
        }
    }

    /** 检查Buff是否与另一个Buff冲突 */
    conflictsWith(_otherBuff: IBuff): boolean {
        // 同类型的持续治疗buff不冲突，可以叠加
        return false;
    }

    /** 播放应用特效 */
    private playApplyEffect(): void {
        console.log(`Playing apply effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的特效播放逻辑
    }

    /** 播放治疗特效 */
    private playHealEffect(): void {
        console.log(`Playing heal effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的治疗特效播放逻辑
    }

    /** 停止特效 */
    private stopEffect(): void {
        console.log(`Stopping effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的特效停止逻辑
    }

    /** 获取调试信息 */
    getDebugInfo() {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            healPerSecond: this._healPerSecond,
            totalHealRemaining: this.getEffectValue("total_heal"),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    }
}
