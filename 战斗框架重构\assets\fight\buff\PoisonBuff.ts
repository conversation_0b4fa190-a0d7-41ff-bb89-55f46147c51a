import { EventManager } from "../systems/EventManager";
import FightEvent from "../types/FightEvent";
import { IBuff, BuffType, IBuffPeriodicEffect, BuffPeriodicEffectType } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { IAttributeModifier, AttributeModifierType } from "../types/ICharacterAttributes";
import { DamageType } from "../types/IDamage";
import { AttributeModifier } from "../characters/AttributeModifier";

/**
 * 毒素Debuff
 * 持续造成毒素伤害并降低目标的生命恢复效果
 */
export class PoisonBuff implements IBuff {
    private _id: string;
    private _name: string = "中毒";
    private _description: string = "持续受到毒素伤害，生命恢复效果降低";
    private _type: BuffType = BuffType.DEBUFF;
    private _duration: number;
    private _remainingTime: number;
    private _stackCount: number = 1;
    private _maxStack: number = 10;
    private _caster: ICharacter;
    private _target: ICharacter;
    private _isExpired: boolean = false;
    private _attributeModifiers: IAttributeModifier[] = [];
    private _eventManager: EventManager;

    // 毒素配置
    private _damagePerSecond: number;
    private _damageInterval: number = 1.0; // 每秒触发一次
    private _lastDamageTime: number = 0;
    private _healingReduction: number = 0.5; // 治疗效果减少50%

    // 视觉效果
    private _iconPath: string = "icons/buffs/poison";
    private _effectPrefabPath: string = "prefabs/effects/PoisonAura";

    constructor(caster: ICharacter, target: ICharacter, duration: number, damagePerSecond: number) {
        this._id = `poison_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._damagePerSecond = damagePerSecond;
        this._eventManager = EventManager.createLocal(`buff_${this._id}`);

        this._description = `每秒受到${damagePerSecond}点毒素伤害，治疗效果降低${Math.round(this._healingReduction * 100)}%，持续${duration}秒`;

        // 创建属性修改器
        this.createAttributeModifiers();
    }

    // 实现IBuff接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get type(): BuffType { return this._type; }
    get duration(): number { return this._duration; }
    get remainingTime(): number { return this._remainingTime; }
    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }
    get stackCount(): number { return this._stackCount; }
    set stackCount(value: number) {
        const oldStack = this._stackCount;
        this._stackCount = Math.max(0, Math.min(value, this._maxStack));
        if (oldStack !== this._stackCount) {
            this.updateAttributeModifiers();
        }
    }
    get maxStack(): number { return this._maxStack; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter { return this._target; }
    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }
    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }
    get iconPath(): string { return this._iconPath; }
    get effectPrefabPath(): string { return this._effectPrefabPath; }

    get periodicEffect() {
        return {
            interval: this._damageInterval,
            effectType: BuffPeriodicEffectType.DAMAGE_OVER_TIME,
            value: this._damagePerSecond,
            stackable: true
        };
    }

    /** 创建属性修改器 */
    private createAttributeModifiers(): void {
        // 生命恢复效果减少修改器
        const healingReductionModifier = new AttributeModifier(
            `${this._id}_healing_reduction`,
            "毒素治疗减少",
            "healingReceived",
            AttributeModifierType.PERCENTAGE,
            -this._healingReduction * this._stackCount,
            this._duration
        );

        this._attributeModifiers = [healingReductionModifier];
    }

    /** 更新属性修改器 */
    private updateAttributeModifiers(): void {
        for (const modifier of this._attributeModifiers) {
            const attributeModifier = modifier as AttributeModifier;
            if (attributeModifier.attributeName === "healingReceived") {
                attributeModifier.setValue(-this._healingReduction * this._stackCount);
            }
        }
    }

    /** Buff被添加时触发 */
    onApply(): void {
        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);

        // 应用属性修改器到目标
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.addModifier(modifier);
        }

        // 播放中毒特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }

        // 触发事件
        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent.characterPoisoned, { target: this._target, caster: this._caster });
    }

    /** Buff每帧更新时触发 */
    onTick(deltaTime: number): void {
        this._lastDamageTime += deltaTime;

        // 检查是否到了造成伤害的时间
        if (this._lastDamageTime >= this._damageInterval) {
            this.dealPoisonDamage();
            this._lastDamageTime = 0;
        }

        // 更新属性修改器
        for (const modifier of this._attributeModifiers) {
            modifier.update(deltaTime);
        }
    }

    /** Buff被移除时触发 */
    onRemove(): void {
        console.log(`${this._name} removed from ${this._target.characterName}`);

        // 移除属性修改器
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.removeModifier(modifier.id);
        }

        this.stopEffect();
        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });
    }

    /** 造成毒素伤害 */
    private dealPoisonDamage(): void {
        if (this._target && !this._target.isDead) {
            const totalDamage = this._damagePerSecond * this._stackCount;

            // 直接造成毒素伤害
            this._target.takeDamage(totalDamage, this._caster);
            console.log(`${this._target.characterName} takes ${totalDamage} poison damage from ${this._name} (Stack: ${this._stackCount})`);

            // 播放毒素伤害特效
            this.playDamageEffect();

            // 触发毒素伤害事件
            this._eventManager.emit(FightEvent.poisonDamageDealt, {
                target: this._target,
                caster: this._caster,
                damage: totalDamage,
                source: this
            });
        }
    }

    /** 更新Buff状态 */
    update(deltaTime: number): boolean {
        if (this._isExpired) {
            return true;
        }

        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);

        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }

        return false;
    }

    /** 刷新Buff持续时间 */
    refresh(): void {
        this._remainingTime = this._duration;
        this._lastDamageTime = 0;

        // 刷新属性修改器时间
        for (const modifier of this._attributeModifiers) {
            modifier.remainingTime = this._duration;
        }

        console.log(`${this._name} refreshed on ${this._target.characterName}`);
    }

    /** 增加叠加层数 */
    addStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);

        if (this._stackCount > oldStack) {
            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });
        }
    }

    /** 减少叠加层数 */
    removeStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);

        if (this._stackCount < oldStack) {
            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });
        }
    }

    /** 获取Buff的当前效果值 */
    getEffectValue(effectType: string): number {
        switch (effectType) {
            case "damage_per_second":
                return this._damagePerSecond * this._stackCount;
            case "total_damage_remaining":
                return this._damagePerSecond * this._stackCount * this._remainingTime;
            case "healing_reduction":
                return this._healingReduction * this._stackCount;
            default:
                return 0;
        }
    }

    /** 检查Buff是否与另一个Buff冲突 */
    conflictsWith(_otherBuff: IBuff): boolean {
        // 毒素buff可以与其他毒素叠加，不冲突
        return false;
    }

    /** 检查是否可以被净化 */
    canBeDispelled(): boolean {
        return true; // 毒素可以被净化技能移除
    }

    /** 播放应用特效 */
    private playApplyEffect(): void {
        console.log(`Playing poison effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的中毒特效播放逻辑
    }

    /** 播放伤害特效 */
    private playDamageEffect(): void {
        console.log(`Playing poison damage effect on ${this._target.characterName}`);
        // 这里应该实现毒素伤害的视觉特效
    }

    /** 停止特效 */
    private stopEffect(): void {
        console.log(`Stopping poison effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的特效停止逻辑
    }

    /** 获取调试信息 */
    getDebugInfo() {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            damagePerSecond: this._damagePerSecond,
            totalDamageRemaining: this.getEffectValue("total_damage_remaining"),
            healingReduction: this.getEffectValue("healing_reduction"),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    }
}
