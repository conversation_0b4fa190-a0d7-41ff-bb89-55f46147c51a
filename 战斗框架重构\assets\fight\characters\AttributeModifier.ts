import { ICharacterAttributes, IAttributeModifier, AttributeModifierType } from "../types/ICharacterAttributes";

/**
 * 属性修改器实现类
 */
export class AttributeModifier implements IAttributeModifier {
    private _id: string;
    private _name: string;
    private _type: AttributeModifierType;
    private _value: number;
    private _duration: number;
    private _remainingTime: number;
    private _attributeName: string;

    constructor(
        id: string,
        name: string,
        attributeName: string,
        type: AttributeModifierType,
        value: number,
        duration: number = -1
    ) {
        this._id = id;
        this._name = name;
        this._attributeName = attributeName;
        this._type = type;
        this._value = value;
        this._duration = duration;
        this._remainingTime = duration;
    }

    // 实现IAttributeModifier接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get type(): AttributeModifierType { return this._type; }
    get value(): number { return this._value; }
    get duration(): number { return this._duration; }
    get remainingTime(): number { return this._remainingTime; }
    set remainingTime(value: number) { this._remainingTime = value; }

    // 额外属性
    get attributeName(): string { return this._attributeName; }

    /**
     * 应用修改器到属性
     */
    apply(attributes: ICharacterAttributes): void {
        const currentValue = attributes.getCurrentAttributeValue(this._attributeName);
        let newValue = currentValue;

        switch (this._type) {
            case AttributeModifierType.ADD:
                newValue = currentValue + this._value;
                break;
            case AttributeModifierType.MULTIPLY:
                newValue = currentValue * this._value;
                break;
            case AttributeModifierType.PERCENTAGE:
                newValue = currentValue * (1 + this._value);
                break;
            case AttributeModifierType.OVERRIDE:
                newValue = this._value;
                break;
        }

        attributes.setAttributeValue(this._attributeName, newValue);
        console.log(`Applied ${this._name}: ${this._attributeName} ${currentValue} -> ${newValue}`);
    }

    /**
     * 移除修改器效果
     */
    remove(_attributes: ICharacterAttributes): void {
        // 这里需要恢复原始值，但由于可能有多个修改器，
        // 实际实现中应该重新计算所有修改器
        console.log(`Removed ${this._name} from ${this._attributeName}`);
    }

    /**
     * 更新修改器
     */
    update(deltaTime: number): boolean {
        if (this._duration < 0) {
            return false; // 永久修改器
        }

        this._remainingTime -= deltaTime;
        return this._remainingTime <= 0;
    }

    /**
     * 设置新的值
     */
    setValue(value: number): void {
        this._value = value;
    }

    /**
     * 获取调试信息
     */
    getDebugInfo(): any {
        return {
            id: this._id,
            name: this._name,
            attributeName: this._attributeName,
            type: this._type,
            value: this._value,
            duration: this._duration,
            remainingTime: this._remainingTime
        };
    }
}
