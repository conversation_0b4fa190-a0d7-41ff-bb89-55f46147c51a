import { TimelineManager } from "../systems/TimelineManager";
import { Timeline, TimelineNode } from "../timeline/Timeline";
import { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, PlayEffectTimelineEvent } from "../timeline/TimelineEvents";
import { IBuff } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { DamageType } from "../types/IDamage";
import { ISkill, SkillType, SkillTargetType } from "../types/ISkill";
import { ITimeline, ITimelineEvent, TimelineEventType } from "../types/ITimeline";
import SkillName from "../types/SkillName";
import { StunBuff } from "../buff/StunBuff";

/**
 * 雷暴术技能
 * 范围攻击技能，在指定区域召唤雷电攻击所有敌人
 */
export class ThunderStormSkill implements ISkill {
    private _id: string = SkillName.thunder_storm;
    private _name: string = "雷暴术";
    private _description: string = "在目标区域召唤雷暴，对范围内所有敌人造成雷电伤害并有概率眩晕";
    private _cooldown: number = 8.0;
    private _remainingCooldown: number = 0;
    private _mpCost: number = 60;
    private _staminaCost: number = 0;
    private _level: number = 1;
    private _type: SkillType = SkillType.ACTIVE;
    private _targetType: SkillTargetType = SkillTargetType.GROUND;
    private _range: number = 400;
    private _timeline: ITimeline | null = null;
    private _passiveBuffs: IBuff[] = [];

    /** 技能配置 */
    private _config = {
        animationName: "skill_thunder_storm",
        soundId: "thunder_cast",
        effectPath: "prefabs/effects/ThunderStorm",
        areaRadius: 150, // 影响范围半径
        damage: 0, // 0表示使用施法者的魔法攻击力
        damageMultiplier: 2.0, // 伤害倍率
        stunChance: 0.3, // 眩晕概率
        stunDuration: 2.0, // 眩晕持续时间
        lightningCount: 5, // 闪电数量
        lightningInterval: 0.3 // 闪电间隔
    };

    // 实现ISkill接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get cooldown(): number { return this._cooldown; }
    get remainingCooldown(): number { return this._remainingCooldown; }
    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }
    get mpCost(): number { return this._mpCost; }
    get staminaCost(): number { return this._staminaCost; }
    get level(): number { return this._level; }
    get type(): SkillType { return this._type; }
    get targetType(): SkillTargetType { return this._targetType; }
    get range(): number { return this._range; }
    get timeline(): ITimeline { return this._timeline!; }
    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }
    get canUse(): boolean {
        return this._remainingCooldown <= 0;
    }

    /** 检查是否可以对目标使用技能 */
    canCastOn(_caster: ICharacter, _target?: ICharacter): boolean {
        // 地面目标技能，不需要特定目标
        return true;
    }

    /** 检查资源消耗 */
    checkResourceCost(caster: ICharacter): boolean {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    }

    /** 消耗资源 */
    consumeResources(caster: ICharacter): void {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    }

    /** 释放技能 */
    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {
        if (!position) {
            console.warn("ThunderStormSkill requires a target position");
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }

        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);

        // 将Timeline添加到TimelineManager进行管理
        const timelineManager = TimelineManager.getInstance();
        timelineManager.addTimeline(this._timeline);

        console.log(`${caster.characterName} casts ${this._name} at position (${position.x}, ${position.y})`);
        return true;
    }

    /** 创建技能Timeline */
    private createTimeline(caster: ICharacter, _target?: ICharacter, _targets?: ICharacter[], position?: cc.Vec3): ITimeline {
        const timelineId = `${this._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const timeline = new Timeline(timelineId, this._name, 3.0, caster);

        // 0.0s: 播放施法动画
        const castNode = new TimelineNode(
            `${timelineId}_cast`,
            0.0,
            new PlayAnimationTimelineEvent("cast_animation", this._config.animationName),
            false
        );
        timeline.addNode(castNode);

        // 0.5s: 播放施法音效
        const soundNode = new TimelineNode(
            `${timelineId}_sound`,
            0.5,
            new PlaySoundTimelineEvent("cast_sound", this._config.soundId),
            false
        );
        timeline.addNode(soundNode);

        // 1.0s: 开始雷暴效果
        const stormStartNode = new TimelineNode(
            `${timelineId}_storm_start`,
            1.0,
            new PlayEffectTimelineEvent("storm_effect", this._config.effectPath, true, position),
            false
        );
        timeline.addNode(stormStartNode);

        // 1.2s - 2.8s: 连续闪电攻击
        for (let i = 0; i < this._config.lightningCount; i++) {
            const lightningTime = 1.2 + i * this._config.lightningInterval;
            const lightningNode = new TimelineNode(
                `${timelineId}_lightning_${i}`,
                lightningTime,
                new LightningStrikeEvent(`lightning_${i}`, caster, position!, this._config.areaRadius, this.calculateDamage(caster)),
                false
            );
            timeline.addNode(lightningNode);
        }

        return timeline;
    }

    /** 计算伤害 */
    private calculateDamage(caster: ICharacter): number {
        if (this._config.damage > 0) {
            return this._config.damage;
        }
        const baseMagicAttack = caster.attributes.magicAttack;
        return Math.floor(baseMagicAttack * this._config.damageMultiplier);
    }

    /** 更新技能冷却 */
    update(deltaTime: number): void {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    }

    /** 重置冷却时间 */
    resetCooldown(): void {
        this._remainingCooldown = 0;
    }

    /** 升级技能 */
    levelUp(): void {
        this._level++;
        this._mpCost = Math.max(40, this._mpCost - 3);
        this._cooldown = Math.max(5.0, this._cooldown - 0.5);
        this._range += 30;
        this._config.areaRadius += 10;
        this._config.damageMultiplier += 0.2;
        this._config.stunChance = Math.min(0.6, this._config.stunChance + 0.05);
        console.log(`${this._name} leveled up to ${this._level}`);
    }

    /** 获取技能信息 */
    getSkillInfo() {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            areaRadius: this._config.areaRadius,
            damageMultiplier: this._config.damageMultiplier,
            stunChance: this._config.stunChance
        };
    }
}

/**
 * 闪电攻击Timeline事件
 */
class LightningStrikeEvent implements ITimelineEvent {
    private _id: string;
    private _type: TimelineEventType = TimelineEventType.DAMAGE;
    private _caster: ICharacter;
    private _position: cc.Vec3;
    private _radius: number;
    private _damage: number;

    constructor(id: string, caster: ICharacter, position: cc.Vec3, radius: number, damage: number) {
        this._id = id;
        this._caster = caster;
        this._position = position;
        this._radius = radius;
        this._damage = damage;
    }

    get id(): string { return this._id; }
    get type(): TimelineEventType { return this._type; }

    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {
        // 查找范围内的所有敌人
        const enemies = this.findEnemiesInRange();

        for (const enemy of enemies) {
            if (!enemy.isDead) {
                // 造成雷电伤害
                const damageInfo = {
                    amount: this._damage,
                    type: DamageType.MAGIC,
                    source: this._caster,
                    isCritical: false,
                    element: "thunder"
                };

                enemy.takeDamage(damageInfo.amount, this._caster);
                console.log(`Lightning strikes ${enemy.characterName} for ${this._damage} thunder damage`);

                // 眩晕概率检查
                if (Math.random() < 0.3) { // 30%概率眩晕
                    console.log(`${enemy.characterName} is stunned by lightning!`);
                    const stunBuff = new StunBuff(this._caster, enemy, 2.0);
                    enemy.buffManager.addBuff(stunBuff);
                }
            }
        }

        // 播放闪电特效和音效
        this.playEffect("prefabs/effects/LightningStrike", this._position);
        this.playSound("lightning_strike");
    }

    /** 查找范围内的敌人 */
    private findEnemiesInRange(): ICharacter[] {
        // 这里需要实现范围查找逻辑
        // 暂时返回空数组，实际应该通过BattleManager或场景管理器查找
        console.log(`Searching for enemies in radius ${this._radius} around position (${this._position.x}, ${this._position.y})`);
        return [];
    }

    playEffect?(effectId: string, position?: cc.Vec3): void {
        console.log(`Playing effect ${effectId} at position (${position?.x}, ${position?.y})`);
    }

    playSound?(soundId: string): void {
        console.log(`Playing sound ${soundId}`);
    }
}
