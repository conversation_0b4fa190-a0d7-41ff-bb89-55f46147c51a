import { TimelineManager } from "./TimelineManager";
import { BulletManager } from "./BulletManager";
import { EventManager } from "./EventManager";
import FightEvent from "../types/FightEvent";
import { ICharacter } from "../types/ICharacter";
import { CharacterRole } from "../types/CharacterTypes";

/**
 * 战斗管理器 - 统一管理战斗流程
 * 负责协调各个系统的工作
 */
export class BattleManager {
    private static _instance: BattleManager;
    private _timelineManager: TimelineManager;
    private _bulletManager: BulletManager;
    private _eventManager: EventManager;
    private _isInBattle: boolean = false;
    private _battleDuration: number = 0;
    private _participants: Set<ICharacter> = new Set();
    private _battleId: string = "";

    constructor() {
        this._timelineManager = TimelineManager.getInstance();
        this._bulletManager = BulletManager.getInstance();
        this._eventManager = EventManager.getGlobal();
        this.setupEventListeners();
        console.log("BattleManager using global EventManager:", this._eventManager.instanceId);
    }
    /** 获取单例实例 */
    static getInstance(): BattleManager {
        if (!BattleManager._instance) {
            BattleManager._instance = new BattleManager();
        }
        return BattleManager._instance;
    }
    /** 获取战斗状态 */
    get isInBattle(): boolean { return this._isInBattle; }
    /** 获取战斗时长 */
    get battleDuration(): number { return this._battleDuration; }
    /** 获取参战者列表 */
    get participants(): ReadonlyArray<ICharacter> { return Array.from(this._participants); }
    /** 获取TimelineManager实例 */
    get timelineManager(): TimelineManager { return this._timelineManager; }
    /** 获取BulletManager实例 */
    get bulletManager(): BulletManager { return this._bulletManager; }

    /** 开始战斗 */
    startBattle(battleId: string, participants: ICharacter[]): void {
        if (this._isInBattle) {
            console.warn("Battle is already in progress");
            return;
        }
        this._battleId = battleId;
        this._isInBattle = true;
        this._battleDuration = 0;
        this._participants.clear();
        // 添加参战者
        for (const participant of participants) {
            this._participants.add(participant);
        }
        // 清理之前的Timeline
        this._timelineManager.clearAll();
        console.log(`Battle ${battleId} started with ${participants.length} participants`);
        // 发送全局战斗开始事件
        EventManager.emit(FightEvent.battleStarted, {
            battleId: this._battleId,
            participants: Array.from(this._participants)
        });
        // 发送局部事件（用于内部管理）
        this._eventManager.emit(FightEvent.battleStarted, {
            battleId: this._battleId,
            participants: Array.from(this._participants)
        });
    }

    /** 结束战斗 */
    endBattle(reason: string = "normal"): void {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        // 暂停所有Timeline
        this._timelineManager.pauseAll();
        console.log(`Battle ${this._battleId} ended. Reason: ${reason}, Duration: ${this._battleDuration}s`);
        // 发送全局战斗结束事件
        EventManager.emit(FightEvent.battleEnded, {
            battleId: this._battleId,
            reason,
            duration: this._battleDuration,
            participants: Array.from(this._participants)
        });
        // 发送局部事件
        this._eventManager.emit(FightEvent.battleEnded, {
            battleId: this._battleId,
            reason,
            duration: this._battleDuration,
            participants: Array.from(this._participants)
        });
        // 重置状态
        this._isInBattle = false;
        this._battleId = "";
        this._participants.clear();
        // 清理所有Timeline和子弹
        this._timelineManager.clearAll();
        this._bulletManager.clearAllBullets();
    }

    /** 暂停战斗 */
    pauseBattle(): void {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        this._timelineManager.pauseAll();
        console.log(`Battle ${this._battleId} paused`);
        this._eventManager.emit(FightEvent.battlePaused, { battleId: this._battleId });
    }

    /** 恢复战斗 */
    resumeBattle(): void {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        this._timelineManager.resumeAll();
        console.log(`Battle ${this._battleId} resumed`);
        this._eventManager.emit(FightEvent.battleResumed, { battleId: this._battleId });
    }

    /** 添加参战者 */
    addParticipant(character: ICharacter): void {
        this._participants.add(character);
        console.log(`${character.characterName} joined the battle`);
        this._eventManager.emit(FightEvent.participantAdded, { battleId: this._battleId, character });
    }

    /** 移除参战者 */
    removeParticipant(character: ICharacter): void {
        if (this._participants.delete(character)) {
            // 移除该角色相关的所有Timeline
            this._timelineManager.removeTimelinesByCaster(character);
            console.log(`${character.characterName} left the battle`);
            this._eventManager.emit(FightEvent.participantRemoved, { battleId: this._battleId, character });
            // 检查战斗是否应该结束
            this.checkBattleEnd();
        }
    }

    /** 更新战斗状态 */
    update(deltaTime: number): void {
        if (!this._isInBattle) {
            return;
        }
        this._battleDuration += deltaTime;
        this._timelineManager.update(deltaTime);
        this._bulletManager.update(deltaTime);
        // 更新参战者状态
        for (const participant of this._participants) {
            if (participant.update) {
                participant.update(deltaTime);
            }
        }
        this.checkBattleEnd();
    }

    /** 检查战斗是否应该结束 */
    private checkBattleEnd(): void {
        if (!this._isInBattle) {
            return;
        }
        // 检查是否有存活的敌对双方
        const alivePlayers = Array.from(this._participants).filter(p => !p.isDead && p.role === CharacterRole.HERO);
        const aliveEnemies = Array.from(this._participants).filter(p => !p.isDead && p.role === CharacterRole.ENEMY);
        if (alivePlayers.length === 0) {
            this.endBattle("players_defeated");
        } else if (aliveEnemies.length === 0) {
            this.endBattle("enemies_defeated");
        }
    }

    /** 设置事件监听器 */
    private setupEventListeners(): void {
        // 监听角色死亡事件
        this._eventManager.on(FightEvent.characterDied, (event: any) => {
            const character = event.character as ICharacter;
            console.log(`${character.characterName} died in battle`);
            // 移除死亡角色的Timeline
            this._timelineManager.removeTimelinesByCaster(character);
            // 检查战斗是否结束
            this.checkBattleEnd();
        });
        // 监听Timeline事件
        this._eventManager.on(FightEvent.timelineCompleted, () => {
            console.log(`Timeline completed in battle ${this._battleId}`);
        });
    }

    /** 获取战斗统计信息 */
    getBattleStats() {
        return {
            battleId: this._battleId,
            isInBattle: this._isInBattle,
            duration: this._battleDuration,
            participantCount: this._participants.size,
            timelineStats: this._timelineManager.getStats(),
            bulletStats: this._bulletManager.getStats(),
            participants: Array.from(this._participants).map(p => ({
                characterName: p.characterName,
                role: p.role,
                isDead: p.isDead,
                hp: p.attributes.currentHp || 0,
                maxHp: p.attributes.maxHp || 0
            }))
        };
    }
    /** 打印调试信息 */
    printDebugInfo(): void {
        console.log("BattleManager Debug Info:", this.getBattleStats());
        this._timelineManager.printDebugInfo();
    }
    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
    get eventManager(): EventManager {
        return this._eventManager;
    }

    /** 清理管理器 */
    cleanup(): void {
        this.endBattle("cleanup");
        this._eventManager.cleanup();
        this._timelineManager.cleanup();
        this._bulletManager.cleanup();
    }
}
