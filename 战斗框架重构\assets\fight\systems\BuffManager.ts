import FightEvent from "../types/FightEvent";
import { IBuffManager, IBuff, BuffType } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { EventManager } from "./EventManager";

/*** Buff管理器类*/
export class BuffManager implements IBuffManager {
    private _character: ICharacter;
    private _buffs: Map<string, IBuff> = new Map();
    private _buffsByType: Map<BuffType, Set<string>> = new Map();
    private _eventManager: EventManager;

    constructor(character: ICharacter) {
        this._character = character;
        this._eventManager = EventManager.createLocal(`BuffManager_${character.characterName}`);
        // 初始化按类型分组的映射
        for (const type of Object.values(BuffType)) {
            this._buffsByType.set(type, new Set());
        }
    }
    /** * 获取所有Buff */
    get buffs(): ReadonlyArray<IBuff> {
        return Array.from(this._buffs.values());
    }
    /**
     * 添加Buff
     * @param buff Buff实例
     */
    addBuff(buff: IBuff): void {
        const existingBuff = this._buffs.get(buff.id);
        if (existingBuff) {
            // 如果Buff已存在，处理叠加逻辑
            this.handleBuffStack(existingBuff, buff);
        } else {
            // 添加新Buff
            this._buffs.set(buff.id, buff);
            this._buffsByType.get(buff.type)?.add(buff.id);
            // 触发Buff应用效果
            buff.onApply();
            this._eventManager.emit(FightEvent.buffAdded, { buff, character: this._character });
        }
    }

    /**
     * 移除Buff
     * @param buffId Buff ID
     * @returns 是否移除成功
     */
    removeBuff(buffId: string): boolean {
        const buff = this._buffs.get(buffId);
        if (!buff) {
            return false;
        }
        // 触发Buff移除效果
        buff.onRemove();
        // 从映射中移除
        this._buffs.delete(buffId);
        this._buffsByType.get(buff.type)?.delete(buffId);
        this._eventManager.emit(FightEvent.buffRemoved, { buff, character: this._character });
        return true;
    }
    /**
     * 根据ID获取Buff
     * @param buffId Buff ID
     * @returns Buff实例或null
     */
    getBuff(buffId: string): IBuff | null {
        return this._buffs.get(buffId) || null;
    }
    /**
     * 根据类型获取Buff列表
     * @param type Buff类型
     * @returns Buff列表
     */
    getBuffsByType(type: BuffType): IBuff[] {
        const buffIds = this._buffsByType.get(type);
        if (!buffIds) {
            return [];
        }
        const buffs: IBuff[] = [];
        for (const buffId of buffIds) {
            const buff = this._buffs.get(buffId);
            if (buff) {
                buffs.push(buff);
            }
        }
        return buffs;
    }
    /**
     * 检查是否拥有指定Buff
     * @param buffId Buff ID
     * @returns 是否拥有
     */
    hasBuff(buffId: string): boolean {
        return this._buffs.has(buffId);
    }
    /**
     * 检查是否拥有指定类型的Buff
     * @param type Buff类型
     * @returns 是否拥有
     */
    hasBuffOfType(type: BuffType): boolean {
        const buffIds = this._buffsByType.get(type);
        return buffIds ? buffIds.size > 0 : false;
    }
    /**
     * 获取指定类型的Buff数量
     * @param type Buff类型
     * @returns Buff数量
     */
    getBuffCountByType(type: BuffType): number {
        const buffIds = this._buffsByType.get(type);
        return buffIds ? buffIds.size : 0;
    }
    /** * 清除所有Buff */
    clearAllBuffs(): void {
        const buffsToRemove = Array.from(this._buffs.keys());
        for (const buffId of buffsToRemove) {
            this.removeBuff(buffId);
        }
    }
    /**
     * 清除指定类型的Buff
     * @param type Buff类型
     */
    clearBuffsByType(type: BuffType): void {
        const buffIds = this._buffsByType.get(type);
        if (buffIds) {
            const idsToRemove = Array.from(buffIds);
            for (const buffId of idsToRemove) {
                this.removeBuff(buffId);
            }
        }
    }
    /**
     * 刷新Buff持续时间
     * @param buffId Buff ID
     */
    refreshBuff(buffId: string): void {
        const buff = this._buffs.get(buffId);
        if (buff) {
            buff.refresh();
            this._eventManager.emit(FightEvent.buffRefreshed, { buff, character: this._character });
        }
    }
    /**
     * 增加Buff叠加层数
     * @param buffId Buff ID
     * @param count 增加的层数
     */
    addBuffStack(buffId: string, count: number = 1): void {
        const buff = this._buffs.get(buffId);
        if (buff) {
            buff.addStack(count);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff, character: this._character });
        }
    }
    /**
     * 减少Buff叠加层数
     * @param buffId Buff ID
     * @param count 减少的层数
     */
    removeBuffStack(buffId: string, count: number = 1): void {
        const buff = this._buffs.get(buffId);
        if (buff) {
            buff.removeStack(count);
            // 如果叠加层数为0，移除Buff
            if (buff.stackCount <= 0) {
                this.removeBuff(buffId);
            } else {
                this._eventManager.emit(FightEvent.buffStackChanged, { buff, character: this._character });
            }
        }
    }
    /**
     * 更新所有Buff
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void {
        const expiredBuffs: string[] = [];
        for (const [buffId, buff] of this._buffs) {
            // 更新Buff状态
            const isExpired = buff.update(deltaTime);
            if (isExpired) {
                expiredBuffs.push(buffId);
            } else {
                // 触发Buff每帧效果
                buff.onTick(deltaTime);
            }
        }
        // 移除过期的Buff
        for (const buffId of expiredBuffs) {
            this.removeBuff(buffId);
        }
    }
    /**
     * 处理Buff叠加逻辑
     * @param existingBuff 已存在的Buff
     * @param newBuff 新的Buff
     */
    private handleBuffStack(existingBuff: IBuff, newBuff: IBuff): void {
        if (existingBuff.maxStack > 1) {
            // 可以叠加
            existingBuff.addStack(newBuff.stackCount);
            existingBuff.refresh(); // 刷新持续时间
            this._eventManager.emit(FightEvent.buffStacked, {
                buff: existingBuff,
                character: this._character,
                addedStacks: newBuff.stackCount
            });
        } else {
            // 不能叠加，刷新持续时间
            existingBuff.refresh();
            this._eventManager.emit(FightEvent.buffRefreshed, {
                buff: existingBuff,
                character: this._character
            });
        }
    }
    /**
     * 获取所有Buff的ID列表
     * @returns Buff ID数组
     */
    getAllBuffIds(): string[] {
        return Array.from(this._buffs.keys());
    }

    /**
     * 获取所有Buff列表
     * @returns Buff数组
     */
    getAllBuffs(): IBuff[] {
        return Array.from(this._buffs.values());
    }
    /**
     * 获取Buff统计信息
     * @returns 统计信息
     */
    getBuffStats() {
        const stats = {
            totalBuffs: this._buffs.size,
            buffsByType: {},
            expiredBuffs: 0,
            stackableBuffs: 0
        };
        for (const buff of this._buffs.values()) {
            if (!stats.buffsByType[buff.type]) {
                stats.buffsByType[buff.type] = 0;
            }
            stats.buffsByType[buff.type]++;
            if (buff.isExpired) {
                stats.expiredBuffs++;
            }
            if (buff.maxStack > 1) {
                stats.stackableBuffs++;
            }
        }
        return stats;
    }

    /**
     * 导出Buff数据
     * @returns Buff数据
     */
    exportBuffData() {
        const data = {} as IBuff
        for (const [id, buff] of this._buffs) {
            data[id] = {
                id: buff.id,
                name: buff.name,
                type: buff.type,
                duration: buff.duration,
                remainingTime: buff.remainingTime,
                stackCount: buff.stackCount,
                maxStack: buff.maxStack,
                isExpired: buff.isExpired
            };
        }
        return data;
    }

    /**
     * 获取事件管理器（供外部直接使用，避免包装方法冗余）
     */
    get eventManager(): EventManager {
        return this._eventManager;
    }
    /** * 清理Buff管理器 */
    cleanup(): void {
        this.clearAllBuffs();
        this._buffsByType.clear();
        this._eventManager.cleanup();
    }
    /** * 获取调试信息 */
    getDebugInfo() {
        return {
            characterId: this._character.id,
            buffCount: this._buffs.size,
            buffs: this.exportBuffData(),
            stats: this.getBuffStats()
        };
    }
}
