import { IBulletLauncher } from "../types/IBullet";
import { ICharacter } from "../types/ICharacter";
import { DamageType, DamageTag } from "../types/IDamage";
import { TimelineEventType, ITimeline } from "../types/ITimeline";
import { TimelineEvent } from "./Timeline";
import { BuffModelBeHurtFight } from "../buff/BuffModelBeHurtFight";
import { BulletManager } from "../systems/BulletManager";
import { DamageManager } from "../systems/DamageManager";


/*** 伤害事件*/
export class DamageTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private damageAmount: number,
        private damageType: DamageType = DamageType.PHYSICAL,
        private tags: DamageTag[] = []
    ) { super(id, TimelineEventType.DAMAGE); }

    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        const target = this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn(`DamageTimelineEvent: No valid target found`);
            return;
        }
        // 使用伤害管理器处理复杂的伤害计算
        const damageManager = DamageManager.getInstance();
        const baseDamage = this.damageAmount === 0 ? caster.attributes.attack : this.damageAmount;
        const damageInfo = damageManager.dealDamage(caster, target, baseDamage, this.damageType, this.tags);

        console.log(`[Timeline-Event] ${caster.characterName} deals ${damageInfo.finalDamage} ${this.damageType} damage to ${target.characterName}${damageInfo.isCritical ? ' (Critical!)' : ''}`);
    }
}

/*** 治疗事件*/
export class HealTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private healAmount: number
    ) { super(id, TimelineEventType.HEAL); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        const target = this.getValidTarget(timeline, nodeIndex) || caster; // 默认治疗自己
        console.log(`[Timeline-Event] ${caster.characterName} heals ${target.characterName} for ${this.healAmount}`);
        target.heal(this.healAmount);
    }
}
/** * 子弹发射事件 */
export class FireBulletTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private bulletLauncher: IBulletLauncher,
        private effectPrefabPath?: string,
        private hitAnimationName?: string,
        private soundId?: string
    ) { super(id, TimelineEventType.FIRE_BULLET); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        const target = this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn(`FireBulletTimelineEvent: No valid target found`);
            return;
        }
        console.log(`[Timeline-Event] ${caster.characterName} fires bullet at ${target.characterName}`);
        if (this.soundId) {
            this.playSound(this.soundId);
        }
        this.bulletLauncher.firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        const bullet = this.bulletLauncher.fire(target);

        // 将子弹添加到子弹管理器
        if (bullet) {
            const bulletManager = BulletManager.getInstance();
            bulletManager.addBullet(bullet);
            console.log(`[Timeline-Event] Bullet ${bullet.id} added to BulletManager`);
        }

        if (this.effectPrefabPath) {
            this.playEffect(this.effectPrefabPath, target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        }
    }
}

/*** Buff添加事件*/
export class AddBuffTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private buffId: string,
        private targetSelf: boolean = false
    ) { super(id, TimelineEventType.ADD_BUFF); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        const target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn(`AddBuffTimelineEvent: No valid target found`);
            return;
        }
        console.log(`[Timeline-Event] Adding buff ${this.buffId} to ${target.characterName}`);

        // 简单的 Buff 创建逻辑（可以后续扩展为 BuffFactory）
        const buff = this.createBuff(this.buffId, caster, target);
        if (buff) {
            target.addBuff(buff);
            console.log(`[Timeline-Event] Successfully added buff ${this.buffId} to ${target.characterName}`);
        } else {
            console.warn(`[Timeline-Event] Failed to create buff ${this.buffId}`);
        }
    }

    /** 创建 Buff 实例（简单工厂模式） */
    private createBuff(buffId: string, caster: ICharacter, target: ICharacter): any {
        switch (buffId) {
            case "counter_attack":
            case "hurt_fight":
                return new BuffModelBeHurtFight(caster, target);
            // 可以在这里添加更多 Buff 类型
            default:
                console.warn(`Unknown buff ID: ${buffId}`);
                return null;
        }
    }
}

/*** 动画播放事件*/
export class PlayAnimationTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private animationName: string,
        private loop: boolean = false,
        private targetSelf: boolean = true
    ) { super(id, TimelineEventType.PLAY_ANIMATION); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        const target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn(`PlayAnimationTimelineEvent: No valid target found`);
            return;
        }
        console.log(`[Timeline-Event] Playing animation ${this.animationName} on ${target.characterName}`);
        const spine = target.node.getComponent(sp.Skeleton);
        if (spine) {
            spine.setAnimation(0, this.animationName, this.loop);
        }
    }
}

/*** 音效播放事件*/
export class PlaySoundTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private soundId: string,
        private volume: number = 1.0
    ) { super(id, TimelineEventType.PLAY_SOUND); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        console.log(`[Timeline-Event] Playing sound ${this.soundId}`);
        this.playSound(this.soundId);
    }
}
/*** 特效播放事件*/
export class PlayEffectTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private effectId: string,
        private targetPosition: boolean = true,
        private offset: cc.Vec3 = cc.Vec3.ZERO
    ) { super(id, TimelineEventType.PLAY_EFFECT); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        let position: cc.Vec3;
        if (this.targetPosition) {
            const target = this.getValidTarget(timeline, nodeIndex);
            if (target) {
                position = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
            } else {
                position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
            }
        } else {
            position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
        }
        console.log(`[Timeline-Event] Playing effect ${this.effectId} at position`, position);
        this.playEffect(this.effectId, position);
    }
}
/** * 移动事件 */
export class MoveTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private targetPosition: cc.Vec3,
        private duration: number = 1.0,
        private easing: string = "linear"
    ) { super(id, TimelineEventType.MOVE); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        console.log(`[Timeline-Event] Moving ${caster.characterName} to position`, this.targetPosition);
        cc.tween(caster.node)
            .to(this.duration, { position: this.targetPosition })
            .start()
    }
}
/*** 自定义事件*/
export class CustomTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private customFunction: (timeline: ITimeline, nodeIndex: number) => void
    ) { super(id, TimelineEventType.CUSTOM); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        try {
            this.customFunction(timeline, nodeIndex);
        } catch (error) {
            console.error(`Error executing custom timeline event ${this.id}:`, error);
        }
    }
}
/*** 多目标伤害事件（用于AOE技能）*/
export class MultiTargetDamageTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private damageAmount: number,
        private damageType: DamageType = DamageType.PHYSICAL,
        private tags: DamageTag[] = []
    ) { super(id, TimelineEventType.DAMAGE); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        const caster = timeline.caster;
        const targets = this.getTargets(timeline);
        if (targets.length === 0) {
            console.warn(`MultiTargetDamageTimelineEvent: No valid targets found`);
            return;
        }
        console.log(`[Timeline-Event] ${caster.characterName} deals AOE damage to ${targets.length} targets`);
        // 对所有目标造成伤害
        for (const target of targets) {
            const finalDamage = this.calculateDamage(caster, target);
            target.takeDamage(finalDamage, caster);
        }
    }
    private calculateDamage(caster: ICharacter, target: ICharacter): number {
        const baseAttack = caster.attributes.attack;
        const defense = target.attributes.defense;
        let finalDamage = this.damageAmount;
        if (this.damageAmount === 0) {
            finalDamage = baseAttack;
        }
        if (this.damageType === DamageType.PHYSICAL) {
            finalDamage = Math.max(1, finalDamage - defense);
        }
        return Math.floor(finalDamage);
    }
}
/** * 条件事件（只有满足条件时才执行） */
export class ConditionalTimelineEvent extends TimelineEvent {
    constructor(
        id: string,
        private condition: (timeline: ITimeline) => boolean,
        private wrappedEvent: TimelineEvent
    ) { super(id, TimelineEventType.CUSTOM); }
    execute(timeline: ITimeline, nodeIndex: number): void {
        if (this.condition(timeline)) {
            this.wrappedEvent.execute(timeline, nodeIndex);
        } else {
            console.log(`[Timeline-Event] Condition not met for event ${this.id}`);
        }
    }
}
