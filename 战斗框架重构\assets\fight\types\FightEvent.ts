export default class FightEvent {
    /**攻击状态改变 */
    static attackStateChanged = 'attackStateChanged'
    /**Buff被添加时触发事件 */
    static buffApplied = 'buffApplied'
    /**Buff被移除时触发 */
    static buffRemoved = 'buffRemoved'
    /**刷新Buff持续时间 */
    static buffRefreshed = 'buffRefreshed'
    /**增加叠加层数 */
    static buffStackChanged = 'buffStackChanged'
    /**执行反击 */
    static counterAttack = 'counterAttack'
    /**移动状态改变 */
    static moveStateChanged = 'moveStateChanged'
    /**释放技能 */
    static skillCast = 'skillCast'
    /**添加Buff */
    static buffAdded = 'buffAdded'
    /**受到伤害 */
    static takeDamage = 'takeDamage'
    /**治疗 */
    static heal = 'heal'
    /**死亡 */
    static death = 'death'
    /**设置buff状态 */
    static stateChanged = 'stateChanged'
    /**移除角色 */
    static characterRemoved = 'characterRemoved'
    /**修改生命值 */
    static hpChanged = 'hpChanged'
    /**修改魔法值 */
    static mpChanged = 'mpChanged'
    /**修改耐力 */
    static staminaChanged = 'staminaChanged'
    /**直接设置属性值（用于修改器） */
    static attributeChanged = 'attributeChanged'
    /**buff层级叠加 */
    static buffStacked = 'buffStacked'
    /**子弹击中 */
    static bulletHit = 'bulletHit'
    /**子弹销毁 */
    static bulletDestroyed = 'bulletDestroyed'
    /**子弹射击 */
    static bulletFired = 'bulletFired'
    /**添加技能 */
    static skillAdded = 'skillAdded'
    /**移除技能 */
    static skillRemoved = 'skillRemoved'
    /**重置技能冷却 */
    static skillCooldownReset = 'skillCooldownReset'
    /**重置技能冷却（多个） */
    static allSkillsCooldownReset = 'allSkillsCooldownReset'
    /**使用技能 */
    static skillUsed = 'skillUsed'
    /**添加Timeline */
    static timelineAdded = 'timelineAdded'
    /**移除Timeline */
    static timelineRemoved = 'timelineRemoved'
    /**暂停所有timeline */
    static allTimelinesPaused = 'allTimelinesPaused'
    /**恢复所有timeline */
    static allTimelinesResumed = 'allTimelinesResumed'
    /**清除所有Timeline */
    static allTimelinesCleared = 'allTimelinesCleared'
    /**暂停指定Timeline */
    static timelinePaused = 'timelinePaused'
    /**恢复指定Timeline */
    static timelineResumed = 'timelineResumed'
    /**Timeline完成时的回调 */
    static timelineCompleted = 'timelineCompleted'
    /**Timeline停止时的回调 */
    static timelineStopped = 'timelineStopped'
    /**更新Timeline */
    static completedT = 'completedT'
    /**暂停Timeline */
    static pausedT = 'pausedT'
    /**恢复Timeline */
    static resumedT = 'resumedT'
    /**停止Timeline */
    static stoppedT = 'stoppedT'
    /**重置Timeline */
    static resetT = 'resetT'
    /***跳转到指定时间点timeline */
    static seekedT = 'seekedT'

    // 战斗管理器事件
    /**战斗开始 */
    static battleStarted = 'battleStarted'
    /**战斗结束 */
    static battleEnded = 'battleEnded'
    /**战斗暂停 */
    static battlePaused = 'battlePaused'
    /**战斗恢复 */
    static battleResumed = 'battleResumed'
    /**参战者加入 */
    static participantAdded = 'participantAdded'
    /**参战者离开 */
    static participantRemoved = 'participantRemoved'
    /**角色死亡 */
    static characterDied = 'characterDied'
    /**角色被眩晕 */
    static characterStunned = 'characterStunned'
    /**角色眩晕结束 */
    static characterStunEnded = 'characterStunEnded'
    /**角色中毒 */
    static characterPoisoned = 'characterPoisoned'
    /**毒素伤害 */
    static poisonDamageDealt = 'poisonDamageDealt'
    /**角色被治疗 */
    static characterHealed = 'characterHealed'

}