import { ICharacter } from "./ICharacter";
import { ISkill } from "./ISkill";
import { IDamageInfo } from "./IDamage";
import { ITimeline } from "./ITimeline";
import { IAttributeModifier } from "./ICharacterAttributes";

/*** Buff接口*/
export interface IBuff {
    /** Buff ID */
    readonly id: string;
    /** Buff名称 */
    readonly name: string;
    /** Buff描述 */
    readonly description: string;
    /** Buff类型 */
    readonly type: BuffType;
    /** 持续时间（秒，-1表示永久） */
    readonly duration: number;
    /** 剩余时间 */
    remainingTime: number;
    /** 叠加层数 */
    stackCount: number;
    /** 最大叠加层数 */
    readonly maxStack: number;
    /** 施法者 */
    readonly caster: ICharacter;
    /** 目标 */
    readonly target: ICharacter;
    /** 是否已过期 */
    readonly isExpired: boolean;
    /** 属性修改器列表（解决属性更新Bug的关键） */
    readonly attributeModifiers: ReadonlyArray<IAttributeModifier>;
    /** 周期性效果配置 */
    readonly periodicEffect?: IBuffPeriodicEffect;
    /** 图标路径 */
    readonly iconPath?: string;
    /** 特效预制体路径 */
    readonly effectPrefabPath?: string;
    /** * Buff被添加时触发 */
    onApply(): void;
    /**
     * Buff每帧更新时触发
     * @param deltaTime 时间间隔
     */
    onTick(deltaTime: number): void;
    /** * Buff被移除时触发 */
    onRemove(): void;
    /**
     * 释放技能时触发
     * @param skill 释放的技能
     * @param timeline 技能的Timeline
     * @returns 修改后的Timeline
     */
    onSkillCast?(skill: ISkill, timeline: ITimeline): ITimeline;
    /**
     * 造成伤害时触发
     * @param damageInfo 伤害信息
     * @param target 目标
     * @returns 修改后的伤害信息
     */
    onDealDamage?(damageInfo: IDamageInfo, target: ICharacter): IDamageInfo;
    /**
     * 受到伤害时触发
     * @param damageInfo 伤害信息
     * @param attacker 攻击者
     * @returns 修改后的伤害信息
     */
    onTakeDamage?(damageInfo: IDamageInfo, attacker: ICharacter): IDamageInfo;
    /**
     * 击杀敌人时触发
     * @param victim 被击杀的敌人
     */
    onKill?(victim: ICharacter): void;
    /**
     * 被击杀时触发
     * @param killer 击杀者
     */
    onDeath?(killer: ICharacter): void;
    /**
     * 更新Buff状态
     * @param deltaTime 时间间隔
     * @returns 是否已过期
     */
    update(deltaTime: number): boolean;
    /**
     * 刷新Buff持续时间
     */
    refresh(): void;
    /**
     * 增加叠加层数
     * @param count 增加的层数
     */
    addStack(count?: number): void;
    /**
     * 减少叠加层数
     * @param count 减少的层数
     */
    removeStack(count?: number): void;
    /**
     * 获取Buff的当前效果值
     * @param effectType 效果类型
     * @returns 效果值
     */
    getEffectValue(effectType: string): number;
    /**
     * 检查Buff是否与另一个Buff冲突
     * @param otherBuff 另一个Buff
     * @returns 是否冲突
     */
    conflictsWith(otherBuff: IBuff): boolean;
}

/*** Buff配置接口*/
export interface IBuffConfig {
    /** Buff ID */
    id: string;
    /** Buff名称 */
    name: string;
    /** Buff描述 */
    description: string;
    /** Buff类型 */
    type: BuffType;
    /** 持续时间 */
    duration: number;
    /** 最大叠加层数 */
    maxStack: number;
    /** 图标路径 */
    iconPath?: string;
    /** 特效预制体 */
    effectPrefab?: string;
    /** 音效ID */
    soundId?: string;
    /** 属性修改 */
    attributeModifiers?: IBuffAttributeModifier[];
    /** 周期性效果 */
    periodicEffect?: IBuffPeriodicEffect;
}

/*** Buff属性修改器*/
export interface IBuffAttributeModifier {
    /** 属性名称 */
    attributeName: string;
    /** 修改类型 */
    modifierType: BuffModifierType;
    /** 修改值 */
    value: number;
    /** 是否按叠加层数计算 */
    stackable: boolean;
}
/*** Buff周期性效果*/
export interface IBuffPeriodicEffect {
    /** 触发间隔（秒） */
    interval: number;
    /** 效果类型 */
    effectType: BuffPeriodicEffectType;
    /** 效果值 */
    value: number;
    /** 是否按叠加层数计算 */
    stackable: boolean;
}
/*** Buff类型枚举*/
export enum BuffType {
    /** 增益 */
    BUFF = "buff",
    /** 减益 */
    DEBUFF = "debuff",
    /** 中性 */
    NEUTRAL = "neutral"
}
/*** Buff修改器类型枚举*/
export enum BuffModifierType {
    /** 加法 */
    ADD = "add",
    /** 乘法 */
    MULTIPLY = "multiply",
    /** 百分比 */
    PERCENTAGE = "percentage"
}
/*** Buff周期性效果类型枚举*/
export enum BuffPeriodicEffectType {
    /** 持续伤害 */
    DAMAGE_OVER_TIME = "damage_over_time",
    /** 持续治疗 */
    HEAL_OVER_TIME = "heal_over_time",
    /** 魔法恢复 */
    MP_RECOVERY = "mp_recovery",
    /** 耐力恢复 */
    STAMINA_RECOVERY = "stamina_recovery"
}
/*** Buff管理器接口*/
export interface IBuffManager {
    /** 所有Buff列表 */
    readonly buffs: ReadonlyArray<IBuff>;
    /**
     * 添加Buff
     * @param buff Buff实例
     */
    addBuff(buff: IBuff): void;
    /**
     * 移除Buff
     * @param buffId Buff ID
     */
    removeBuff(buffId: string): void;
    /**
     * 根据ID获取Buff
     * @param buffId Buff ID
     * @returns Buff实例或null
     */
    getBuff(buffId: string): IBuff | null;
    /**
     * 根据类型获取Buff列表
     * @param type Buff类型
     * @returns Buff列表
     */
    getBuffsByType(type: BuffType): IBuff[];
    /**
     * 清除所有Buff
     */
    clearAllBuffs(): void;
    /**
     * 清除指定类型的Buff
     * @param type Buff类型
     */
    clearBuffsByType(type: BuffType): void;
    /**
     * 更新所有Buff
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
}
