import { ICharacter } from "./ICharacter";

/*** 子弹接口*/
export interface IBullet {
    /** 子弹ID */
    readonly id: string;
    /** 子弹类型 */
    readonly type: BulletType;
    /** 子弹节点 */
    readonly node: cc.Node;
    /** 施法者 */
    readonly caster: ICharacter;
    /** 目标 */
    readonly target?: ICharacter;
    /** 目标位置 */
    readonly targetPosition?: cc.Vec3;
    /** 发射位置 */
    readonly firePosition: cc.Vec3;
    /** 当前位置 */
    readonly currentPosition: cc.Vec3;
    /** 移动速度 */
    readonly speed: number;
    /** 生存时间 */
    readonly lifeTime: number;
    /** 已存在时间 */
    timeElapsed: number;
    /** 剩余命中次数 */
    remainingHits: number;
    /** 最大命中次数 */
    readonly maxHits: number;
    /** 是否已碰撞 */
    hasCollided: boolean;
    /** 是否已销毁 */
    readonly isDestroyed: boolean;
    /** 弹道轨迹 */
    readonly trajectory: IBulletTrajectory;
    /**
     * 更新子弹状态
     * @param deltaTime 时间间隔
     * @returns 是否应该销毁
     */
    update(deltaTime: number): boolean;
    /**
     * 命中目标
     * @param target 命中的目标
     * @returns 是否成功命中
     */
    hit(target: ICharacter): boolean;
    /** * 销毁子弹 */
    destroy(): void;
    /**
     * 设置目标
     * @param target 新目标
     */
    setTarget(target: ICharacter): void;
    /**
     * 设置目标位置
     * @param position 目标位置
     */
    setTargetPosition(position: cc.Vec3): void;
}

/** * 子弹类型枚举 */
export enum BulletType {
    /** 直线子弹 */
    STRAIGHT = "straight",
    /** 追踪子弹 */
    HOMING = "homing",
    /** 抛物线子弹 */
    PARABOLIC = "parabolic",
    /** 激光 */
    LASER = "laser",
    /** 范围爆炸 */
    AREA_EXPLOSION = "area_explosion",
    /** 穿透子弹 */
    PIERCING = "piercing",
    /** 反弹子弹 */
    BOUNCING = "bouncing",
    /** 分裂子弹 */
    SPLITTING = "splitting"
}
/*** 子弹轨迹接口*/
export interface IBulletTrajectory {
    /** 轨迹类型 */
    readonly type: TrajectoryType;
    /**
     * 计算下一帧的位置
     * @param bullet 子弹实例
     * @param deltaTime 时间间隔
     * @returns 新位置
     */
    calculateNextPosition(bullet: IBullet, deltaTime: number): cc.Vec3;
    /**
     * 计算朝向角度
     * @param bullet 子弹实例
     * @returns 角度（度）
     */
    calculateRotation(bullet: IBullet): number;
    /**
     * 检查是否到达目标
     * @param bullet 子弹实例
     * @param threshold 距离阈值
     * @returns 是否到达
     */
    hasReachedTarget(bullet: IBullet, threshold: number): boolean;
}

/*** 轨迹类型枚举*/
export enum TrajectoryType {
    /** 直线轨迹 */
    LINEAR = "linear",
    /** 追踪轨迹 */
    HOMING = "homing",
    /** 抛物线轨迹 */
    PARABOLIC = "parabolic",
    /** 贝塞尔曲线轨迹 */
    BEZIER = "bezier",
    /** 螺旋轨迹 */
    SPIRAL = "spiral",
    /** 正弦波轨迹 */
    SINE_WAVE = "sine_wave"
}

/*** 子弹配置接口*/
export interface IBulletConfig {
    /** 子弹ID */
    id: string;
    /** 子弹类型 */
    type: BulletType;
    /** 预制体路径 */
    prefabPath: string;
    /** 移动速度 */
    speed: number;
    /** 生存时间 */
    lifeTime: number;
    /** 最大命中次数 */
    maxHits: number;
    /** 轨迹配置 */
    trajectory: ITrajectoryConfig;
    /** 碰撞检测配置 */
    collision: ICollisionConfig;
    /** 视觉效果配置 */
    visual: IVisualConfig;
    /** 音效配置 */
    audio: IAudioConfig;
    /** 是否没有目标时自动回收 */
    recycleWhenNoTarget?: boolean;
    /** 自定义参数 */
    customParams?: any;
}

/** * 轨迹配置接口 */
export interface ITrajectoryConfig {
    /** 轨迹类型 */
    type: TrajectoryType;
    /** 轨迹参数 */
    params?: any;
}

/** * 碰撞检测配置接口 */
export interface ICollisionConfig {
    /** 碰撞半径 */
    radius: number;
    /** 是否穿透 */
    piercing: boolean;
    /** 碰撞层级 */
    layers: string[];
    /** 碰撞检测频率 */
    checkFrequency: number;
}

/*** 视觉效果配置接口*/
export interface IVisualConfig {
    /** 拖尾效果 */
    trail?: {
        enabled: boolean;
        length: number;
        width: number;
        color: cc.Color;
    };
    /** 旋转效果 */
    rotation?: {
        enabled: boolean;
        speed: number;
        axis: cc.Vec3;
    };
    /** 缩放效果 */
    scale?: {
        enabled: boolean;
        startScale: number;
        endScale: number;
        curve: string;
    };
    /** 命中特效 */
    hitEffect?: {
        prefabPath: string;
        duration: number;
        scale: number;
    };
}
/** * 音效配置接口 */
export interface IAudioConfig {
    /** 发射音效 */
    fireSound?: string;
    /** 飞行音效 */
    flySound?: string;
    /** 命中音效 */
    hitSound?: string;
    /** 销毁音效 */
    destroySound?: string;
}
/*** 子弹发射器接口*/
export interface IBulletLauncher {
    /** 发射器ID */
    readonly id: string;
    /** 施法者 */
    readonly caster: ICharacter;
    /** 子弹配置 */
    readonly bulletConfig: IBulletConfig;
    /** 发射位置 */
    firePosition: cc.Vec3;
    /** 发射方向 */
    fireDirection: cc.Vec3;
    /** 发射角度 */
    fireAngle: number;
    /** 发射速度 */
    fireSpeed: number;
    /**
     * 发射子弹
     * @param target 目标
     * @param targetPosition 目标位置
     * @returns 创建的子弹实例
     */
    fire(target?: ICharacter, targetPosition?: cc.Vec3): IBullet;
    /**
     * 批量发射子弹
     * @param count 发射数量
     * @param spread 散布角度
     * @param target 目标
     * @returns 创建的子弹实例数组
     */
    fireBurst(count: number, spread: number, target?: ICharacter): IBullet[];
    /**
     * 设置发射参数
     * @param position 发射位置
     * @param direction 发射方向
     * @param speed 发射速度
     */
    setFireParams(position: cc.Vec3, direction: cc.Vec3, speed?: number): void;
}

/*** 子弹管理器接口*/
export interface IBulletManager {
    /** 所有活跃的子弹 */
    readonly activeBullets: ReadonlyArray<IBullet>;
    /**
     * 创建子弹
     * @param config 子弹配置
     * @param caster 施法者
     * @param target 目标
     * @param targetPosition 目标位置
     * @returns 创建的子弹实例
     */
    createBullet(
        config: IBulletConfig,
        caster: ICharacter,
        target?: ICharacter,
        targetPosition?: cc.Vec3
    ): IBullet;
    /**
     * 移除子弹
     * @param bulletId 子弹ID
     */
    removeBullet(bulletId: string): void;
    /**
     * 根据施法者移除子弹
     * @param caster 施法者
     */
    removeBulletsByCaster(caster: ICharacter): void;
    /** * 清除所有子弹 */
    clearAllBullets(): void;
    /**
     * 更新所有子弹
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
    /**  * 获取子弹统计信息  */
    getStats(): {
        activeCount: number;
        totalCreated: number;
        totalDestroyed: number;
    };
}
