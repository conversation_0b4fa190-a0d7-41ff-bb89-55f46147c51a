import { CharacterRole } from "../types/CharacterTypes";
import { ISkill } from "./ISkill";
import { IBuff } from "./IBuff";
import { ICharacterAttributes } from "./ICharacterAttributes";
import { SkillManager } from "../systems/SkillManager";
import { BuffManager } from "../systems/BuffManager";

/*** 角色核心接口*/
export interface ICharacter {
    /** 角色唯一ID */
    readonly id: string;
    /** 角色名称 */
    readonly characterName: string;
    /** 角色阵营 */
    readonly role: CharacterRole;
    /** 角色属性 */
    readonly attributes: ICharacterAttributes;
    /** 是否死亡 */
    readonly isDead: boolean;
    /** 角色节点 */
    readonly node: cc.Node;
    /** 技能列表 */
    readonly skills: ReadonlyArray<ISkill>;
    /** Buff列表 */
    readonly buffs: ReadonlyArray<IBuff>;
    /** 技能管理器 */
    readonly skillManager: SkillManager;
    /** Buff管理器 */
    readonly buffManager: BuffManager;
    /**
     * 移动角色
     * @param direction 移动方向
     * @returns 是否移动成功
     */
    move(direction: cc.Vec3): boolean;
    /**
     * 攻击目标
     * @param target 攻击目标
     * @returns 是否攻击成功
     */
    attack(target?: ICharacter): boolean;
    /**
     * 释放技能
     * @param skillName 技能名称
     * @param target 目标
     * @returns 是否释放成功
     */
    castSkill(skillName: string, target?: cc.Node): boolean;
    /**
     * 学习技能
     * @param skill 技能
     */
    learnSkill(skill: ISkill): void;
    /**
     * 添加Buff
     * @param buff Buff信息
     */
    addBuff(buff: IBuff): void;
    /**
     * 移除Buff
     * @param buffId Buff ID
     */
    removeBuff(buffId: string): void;
    /**
     * 受到伤害
     * @param damage 伤害值
     * @param attacker 攻击者
     */
    takeDamage(damage: number, attacker?: ICharacter): void;
    /**
     * 治疗
     * @param healAmount 治疗量
     */
    heal(healAmount: number): void;
    /** * 死亡处理 */
    die(): void;
    /**
     * 更新角色状态
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
}

/*** 角色事件接口*/
export interface ICharacterEvents {
    /** 角色死亡事件 */
    onDeath?: (character: ICharacter) => void;
    /** 角色受到伤害事件 */
    onTakeDamage?: (character: ICharacter, damage: number, attacker?: ICharacter) => void;
    /** 角色治疗事件 */
    onHeal?: (character: ICharacter, healAmount: number) => void;
    /** 技能释放事件 */
    onSkillCast?: (character: ICharacter, skillName: string) => void;
    /** Buff添加事件 */
    onBuffAdded?: (character: ICharacter, buff: IBuff) => void;
    /** Buff移除事件 */
    onBuffRemoved?: (character: ICharacter, buffId: string) => void;
}

/** * 角色控制器接口 */
export interface ICharacterController {
    /** 控制的角色 */
    readonly character: ICharacter;
    /**
     * 初始化控制器
     * @param character 要控制的角色
     */
    initialize(character: ICharacter): void;
    /**
     * 更新控制逻辑
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
    /** * 清理控制器 */
    cleanup(): void;
}
