/**
 * 技能系统接口定义
 * 基于Timeline的技能系统，保留原有的复杂效果支持
 */

import { ICharacter } from "./ICharacter";
import { IBuff } from "./IBuff";
import { ITimeline } from "./ITimeline";

/*** 技能接口*/
export interface ISkill {
    /** 技能ID */
    readonly id: string;
    /** 技能名称 */
    readonly name: string;
    /** 技能描述 */
    readonly description: string;
    /** 冷却时间（秒） */
    readonly cooldown: number;
    /** 当前冷却剩余时间 */
    remainingCooldown: number;
    /** 魔法消耗 */
    readonly mpCost: number;
    /** 耐力消耗 */
    readonly staminaCost: number;
    /** 技能等级 */
    readonly level: number;
    /** 技能类型 */
    readonly type: SkillType;
    /** 目标类型 */
    readonly targetType: SkillTargetType;
    /** 技能范围 */
    readonly range: number;
    /** 技能效果Timeline */
    readonly timeline: ITimeline;
    /** 学习技能时获得的被动Buff */
    readonly passiveBuffs: IBuff[];
    /** 是否可以使用 */
    readonly canUse: boolean;
    /**
     * 检查是否可以对目标使用技能
     * @param caster 施法者
     * @param target 目标
     * @returns 是否可以使用
     */
    canCastOn(caster: ICharacter, target?: ICharacter): boolean;
    /**
     * 释放技能
     * @param caster 施法者
     * @param target 目标
     * @param targets 多个目标
     * @param position 目标位置
     * @returns 是否释放成功
     */
    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean;
    /**
     * 更新技能状态（主要是冷却时间）
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
    /** * 重置冷却时间 */
    resetCooldown(): void;
    /**
     * 技能被添加时的回调（用于多目标技能等特殊情况）
     * @param targets 目标列表
     */
    onAdd?(targets: ICharacter[]): void;
}

/** * 技能效果接口 */
export interface ISkillEffect {
    /** 效果ID */
    readonly id: string;
    /** 效果类型 */
    readonly type: SkillEffectType;
    /**
     * 执行效果
     * @param caster 施法者
     * @param target 目标
     * @param skill 技能
     * @param context 上下文信息
     */
    execute(caster: ICharacter, target: ICharacter, skill: ISkill, context?: any): void;
}

/*** 技能配置接口*/
export interface ISkillConfig {
    /** 技能ID */
    id: string;
    /** 技能名称 */
    name: string;
    /** 技能描述 */
    description: string;
    /** 冷却时间 */
    cooldown: number;
    /** 魔法消耗 */
    mpCost: number;
    /** 耐力消耗 */
    staminaCost: number;
    /** 技能类型 */
    type: SkillType;
    /** 目标类型 */
    targetType: SkillTargetType;
    /** 技能范围 */
    range: number;
    /** 技能效果列表 */
    effects: ISkillEffect[];
    /** 学习技能时获得的Buff */
    passiveBuffs?: IBuff[];
    /** 动画名称 */
    animationName?: string;
    /** 音效ID */
    soundId?: string;
    /** 特效预制体 */
    effectPrefab?: string;
}

/*** 技能类型枚举*/
export enum SkillType {
    /** 主动技能 */
    ACTIVE = "active",
    /** 被动技能 */
    PASSIVE = "passive",
    /** 普通攻击 */
    BASIC_ATTACK = "basic_attack",
    /** 终极技能 */
    ULTIMATE = "ultimate"
}

/*** 技能目标类型枚举*/
export enum SkillTargetType {
    /** 无目标 */
    NONE = "none",
    /** 单个敌人 */
    SINGLE_ENEMY = "single_enemy",
    /** 多个敌人 */
    MULTIPLE_ENEMIES = "multiple_enemies",
    /** 单个友军 */
    SINGLE_ALLY = "single_ally",
    /** 多个友军 */
    MULTIPLE_ALLIES = "multiple_allies",
    /** 自己 */
    SELF = "self",
    /** 地面位置 */
    GROUND = "ground",
    /** 所有敌人 */
    ALL_ENEMIES = "all_enemies",
    /** 所有友军 */
    ALL_ALLIES = "all_allies"
}

/*** 技能效果类型枚举*/
export enum SkillEffectType {
    /** 伤害 */
    DAMAGE = "damage",
    /** 治疗 */
    HEAL = "heal",
    /** 添加Buff */
    ADD_BUFF = "add_buff",
    /** 移除Buff */
    REMOVE_BUFF = "remove_buff",
    /** 召唤 */
    SUMMON = "summon",
    /** 传送 */
    TELEPORT = "teleport",
    /** 击退 */
    KNOCKBACK = "knockback",
    /** 眩晕 */
    STUN = "stun"
}

/*** 技能学习条件接口*/
export interface ISkillRequirement {
    /** 需要的等级 */
    requiredLevel?: number;
    /** 前置技能 */
    prerequisiteSkills?: string[];
    /** 需要的属性 */
    requiredAttributes?: { [key: string]: number };
    /**
     * 检查是否满足学习条件
     * @param character 角色
     * @returns 是否满足条件
     */
    checkRequirement(character: ICharacter): boolean;
}

/** * 攻击动作属性接口 */
export interface IAttackActionProps {
    /** 前摇时间，攻击开始到造成伤害的时间（毫秒） */
    hurtStartTimeMs: number;
    /** 攻击持续时间，从开始到结束的总时间（毫秒） */
    hurtEndTimeMs: number;
    /** 开始造成伤害时的回调 */
    onHurtStart?: () => void;
    /** 攻击结束时的回调 */
    onHurtEnd?: () => void;
    /** 攻击过程中的回调（可选） */
    onAttackProgress?: (progress: number) => void;
}